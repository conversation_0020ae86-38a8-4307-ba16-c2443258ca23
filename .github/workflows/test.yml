name: test

on:
    workflow_dispatch:
    push:
        branches: ['master']

jobs:
    build:
        runs-on: ubuntu-latest

        strategy:
            matrix:
                node-version: [22.x]

        steps:
            - uses: actions/checkout@v3

            - name: Use Node.js ${{ matrix.node-version }}
              uses: actions/setup-node@v3
              with:
                  node-version: ${{ matrix.node-version }}
                  distribution: zulu

            - name: Build
              run: |
                  yarn
                  yarn build
