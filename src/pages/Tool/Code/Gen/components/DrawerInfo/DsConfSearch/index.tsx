import React, { useState, useEffect } from 'react'
import { Select } from 'antd'
import { listConf } from '../../../service'
import { system } from '@/utils/twelvet'

const DsConfSearch: React.FC<{
    onChange?: (value: any, option: any | any[]) => void
}> = props => {

    const [treeData, setTreeData] = useState<any>([])

    const { Option } = Select

    const makeDsConf = async () => {
        try {
            const { data } = await listConf({})

            // 制作数据
            const tree: any = [
                <Option key={'master'} value={'master'}>{'默认数据源'}</Option>
            ]

            data.map((item: {
                id: number
                name: string
            }) => {
                tree.push(
                    <Option key={item.id} value={item.name}>{item.name}</Option>
                )
                return false
            })

            setTreeData(tree)

        } catch (e) {
            system.error(e)
        }
    }

    useEffect(() => {
        makeDsConf()
    }, [])

    return (
        <Select
            // 必须设置props，否则无法取值Search
            {...props}
            placeholder={"数据源"}
            allowClear
            showSearch
            defaultValue={'master'}
        >
            {treeData}
        </Select>
    )
}

export default DsConfSearch
