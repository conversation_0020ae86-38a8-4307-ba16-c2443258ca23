.custom-node {
  background: #ffffff;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 12px;
  min-width: 160px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  &.selected {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  // 连接点容器
  .handle-container {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    z-index: 100;

    &.left {
      left: -6px;
      flex-direction: row;
    }

    &.right {
      right: -6px;
      flex-direction: row-reverse;
    }
  }

  // 工具链接按钮
  .tool-link-button {
    width: 18px;
    height: 18px;
    background: #1890ff;
    border: 2px solid #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: crosshair;
    opacity: 0;
    transition: all 0.2s ease;
    color: white;
    font-size: 10px;
    margin: 0 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 101;

    &:hover {
      background: #40a9ff;
      transform: scale(1.2);
      opacity: 1 !important;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    &:active {
      background: #096dd9;
      transform: scale(1);
    }

    &.left-tool {
      margin-left: 10px;
    }

    &.right-tool {
      margin-right: 10px;
    }
  }

  // 节点悬停时显示工具按钮
  &:hover .tool-link-button {
    opacity: 0.9;
  }

  .node-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .node-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
      font-weight: bold;
    }

    .node-label {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      text-align: center;
      line-height: 1.2;
    }

    .node-description {
      font-size: 12px;
      color: #666;
      text-align: center;
      line-height: 1.3;
      max-width: 140px;
    }
  }

  // 自定义连接点样式
  .custom-handle {
    width: 8px;
    height: 8px;
    border: 2px solid #fff;
    border-radius: 50%;
    transition: all 0.2s ease;
    position: relative;
    cursor: crosshair;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      width: 20px;
      height: 20px;
      border-width: 2px;

      .handle-plus-icon {
        opacity: 1;
        transform: scale(1);
      }
    }

    &.input-handle {
      left: 0;
    }

    &.output-handle {
      right: 0;
    }

    // 加号图标样式
    .handle-plus-icon {
      position: absolute;
      color: white;
      font-size: 10px;
      opacity: 0;
      transform: scale(0.5);
      transition: all 0.2s ease;
      pointer-events: none;
      z-index: 10;
    }
  }
}

// 连接线样式
:global(.react-flow__edge) {
  .react-flow__edge-path {
    stroke: #b1b1b7;
    stroke-width: 2;
  }

  &.selected .react-flow__edge-path {
    stroke: #1890ff;
    stroke-width: 3;
  }

  .react-flow__arrowhead {
    fill: #b1b1b7;
  }

  &.selected .react-flow__arrowhead {
    fill: #1890ff;
  }
}

// 连接线动画
:global(.react-flow__edge.animated) {
  .react-flow__edge-path {
    stroke-dasharray: 5;
    animation: dashdraw 0.5s linear infinite;
  }
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}
